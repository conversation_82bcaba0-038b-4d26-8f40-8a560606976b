﻿using System;
using game.ai.entity;
using pure.database.structure;
using pure.utils.animation;
using pure.utils.task;
using UnityEngine;

namespace dl.sequence.scene {
	public struct SeqAnimEvent {
		public bool Equals(SeqAnimEvent other) {
			return Equals(asset, other.asset) &&
			       Equals(state, other.state) &&
			       dir == other.dir &&
			       frame == other.frame;
		}

		public override bool Equals(object obj) {
			return obj is SeqAnimEvent other && Equals(other);
		}

		public override int GetHashCode() {
			return HashCode.Combine(asset, state, dir, frame);
		}

		public SeqAsset_Dll asset;
		public SeqState state;
		public int dir;
		public int frame;

		public static bool operator ==(SeqAnimEvent a, SeqAnimEvent b) {
			return a.asset == b.asset && a.state == b.state &&
			       a.dir == b.dir &&
			       a.frame == b.frame;
		}

		public static bool operator !=(SeqAnimEvent a, SeqAnimEvent b) {
			return !(a == b);
		}
	}

	public delegate void SeqAnimatorChangeCallback(ref SeqAnimEvent evt);

	public class SeqAnimatorController : IAnimatorController {
		public struct RenderInfo {
			public SeqState state;
			public float pass;
			public int frame;
			public int dir;

			public RenderInfo(SeqState st) {
				state = st;
				pass = 0;
				frame = -1;
				dir = -1;
			}
		}

		public float speed { get; set; } = 1;

		private SeqAsset_Dll _asset;

		public SeqAsset_Dll asset {
			get { return _asset; }
			set {
				if (_asset == value) {
					return;
				}
				_asset = value;
				_invalid.Invalidate();
				_info = new RenderInfo(null);
			}
		}

		private HashMap<int, AnimCollection.Slot> _map = new HashMap<int, AnimCollection.Slot>(16);

		[NonSerialized]
		private RenderInfo _info;

		public RenderInfo renderInfo { get { return _info; } }

		public float normalizeTime { get; private set; }
		public int frame { get { return _info.frame; } }
		public int direction { get { return _info.dir; } }
		public SeqState state { get { return _info.state; } }
		private InvalidateCache _invalid;
		private bool _trigger;
		private int _mainFrame = -1;

		private bool _active;

		public bool active {
			get { return _active; }
			set {
				if (_active == value) return;
				if (reset()) {
					_info = new RenderInfo(null);
					_active = value;
				}
			}
		}

		private bool reset() {
			_map.Clear();
			if (!_asset) return false;
			foreach (SeqParameter a in _asset.parameters) {
				AnimCollection.Slot slot = new AnimCollection.Slot(a.type, a.value);
				_map.Add(a.hash, slot);
			}
			return true;
		}

		public void Restart() {
			_info = new RenderInfo(null);
			_invalid.Invalidate(InvalidateType.Data);
		}

		public void CopyFrom(SeqAnimatorController c) {
			_map.Copy(c._map);
			_invalid.Invalidate();
		}

		public void SetValue(int hash, AnimatorControllerParameterType type, float v) {
			if (!_asset) return;
			if (!_map.Contains(hash)) {
#if UNITY_EDITOR
				//Debug.LogWarning(string.Format("Animator {0} parameter {1} lost", _asset.name, hash), _asset);
#endif
				return;
			}
			AnimCollection.Slot slot = _map[hash];
			slot.value = v;
			slot.frame++;
			_map[hash] = slot;
			if (type == AnimatorControllerParameterType.Trigger) {
				_trigger = true;
			}
			_invalid.Invalidate(InvalidateType.Data);
		}

		private void plus_block() {
			AnimCollection.Slot slot = _map[AnimatorType.Block];
			++slot.value;
			_map[AnimatorType.Block] = slot;
		}

		private void minus_block(bool silent) {
			AnimCollection.Slot slot = _map[AnimatorType.Block];
			--slot.value;
			if (slot.value <= 0) slot.value = 0;
			_map[AnimatorType.Block] = slot;
			if (!silent) {
				_invalid.Invalidate(InvalidateType.Data);
			}
		}

		private void change_state(SeqState st) {
			if (_info.state == st) return;
			if (_info.state != null && _info.state.blockTag) {
				minus_block(true);
			}
			_info = new RenderInfo(st);
			if (_info.state.blockTag) {
				plus_block();
			}
		}

		private bool search_state() {
			if (!_invalid.IsInvalidate(InvalidateType.Data) && state != null) {
				return false;
			}
			_invalid.Clear();
			bool t = _trigger;
			_trigger = false;
			if (t) {
				plus_block();
			}
			SeqState st = asset.Search(_map);
			if (t) {
				minus_block(true);
			}
			if (st == null) {
#if UNITY_EDITOR
				Debug.LogError(string.Format("{0} state == nil", _asset.name));
				_asset.Print(_map);
#endif
				return false;
			}
			if (st == _info.state) {
				if (t) {
					_info.pass = 0;
					_info.frame = 0;
				}
				return false;
			}
			change_state(st);
			return true;
		}

		public void SetMainFrame(int v) {
			_mainFrame = v;
		}

		public bool Update(float orient) {
			if (!_asset) return false;
			bool dirty = search_state();
			if (state == null) {
				return false;
			}
			_info.pass += Time.deltaTime * speed;
			float pass = _info.pass;
			float pert = 1.0f / state.frameRate;
			int total = state.directions[0].frames.Length;
			float full = total * pert;
			int loop = (int)(pass / full);
			float mod = pass - loop * full;
			int fr = (int)(mod / pert);
			if (!state.loop && loop >= 1) {
				fr = total - 1;
			}
			normalizeTime = mod / full;
			if (_mainFrame != -1) {
				fr = _mainFrame;
			}
			int dir = state.CalcDirection(orient);
			if (fr != _info.frame || dir != _info.dir) {
				_info.frame = fr;
				_info.dir = dir;
				dirty = true;
			}
			if (loop > 0 && state.blockTag && !state.loop) {
				minus_block(false);
			}
			return dirty;
		}

		public bool GetSlot(int hash, out AnimCollection.Slot slot) {
			if (_map.Contains(hash)) {
				slot = _map[hash];
				return true;
			}
			slot = new AnimCollection.Slot();
			return false;
		}

		public bool GetParameterType(int n, out AnimatorControllerParameterType t) {
			t = AnimatorControllerParameterType.Int;
			if (!_asset) return false;
			foreach (SeqParameter p in _asset.parameters) {
				if (p.hash == n) {
					t = p.type;
					return true;
				}
			}
			return false;
		}

		public void GetParameter(out string[] names, out AnimatorControllerParameter[] parameters) {
			if (!_asset) {
				names = ZeroBuffer<string>.Buffer;
				parameters = ZeroBuffer<AnimatorControllerParameter>.Buffer;
				return;
			}
			names = new string[_asset.parameters.Length];
			parameters = new AnimatorControllerParameter[asset.parameters.Length];
			for (int i = 0; i < asset.parameters.Length; ++i) {
				names[i] = asset.parameters[i].name;
				parameters[i] = new AnimatorControllerParameter {
					name = asset.parameters[i].name,
					type = asset.parameters[i].type,
				};
			}
		}
	}
}