﻿
using UnityEngine;
#if UNITY_EDITOR

using System.Collections.Generic;
using pure.database.structure;
using pure.refactor.attributes;
using pure.utils.dlltools;
using pure.utils.json;
using pure.utils.memory;
using UnityEditor;
using XLua;

#endif

namespace dl.config {
    public partial class PointGroup : MonoBehaviour { }

#if UNITY_EDITOR
    [MonoDll("point_group", MonoDllTags.Server)]
    public partial class PointGroup : IJsonable {
        [DontExportServer]
        public Color color = Color.green;

        [ComponentGUID(ComponentGUIDAttribute.Mode.Long)]
        public long id;

        public GameObject[] points = new GameObject[0];

        public GameObject core;

        public JsonDataUsage jsonUsage { get { return JsonDataUsage.ClientSide | JsonDataUsage.ServerSide; } }

        private Vector3 get_center() {
            if (core) {
                return core.transform.position;
            }
            points = purge();
            if (points.Length == 0) {
                return transform.position;
            }
            Vector3 big = new Vector3();
            foreach (GameObject go in points) {
                big += go.transform.position;
            }
            return big / points.Length;
        }

        private GameObject[] purge() {
            if (points == null) {
                return new GameObject[0];
            }
            bool err = false;
            foreach (GameObject go in points) {
                if (!go) {
                    err = true;
                    break;
                }
            }
            if (!err) {
                return points;
            }
            List<GameObject> gos = new List<GameObject>();
            foreach (GameObject p in points) {
                if (!p) {
                    continue;
                }
                gos.Add(p);
            }
            return gos.ToArray();
        }

        [BlackList]
        public JsonObject ToJson(JsonDataUsage usage) {
            JsonObject jo = new JsonObject {
                [nameof(id)] = id,
                ["monoType"] = "point_group",
            };
            points = purge();
            if (points == null || points.Length == 0) {
                points = ZeroBuffer<GameObject>.Buffer;
                Debug.LogError(string.Format("[PointGroup] {0} / {1} points is Empty",
                                             gameObject.scene.name,
                                             transform.GetFullPath()));
            }
            if ((usage & JsonDataUsage.ClientSide) != 0) {
                jo[nameof(core)] = get_center();
            } else {
                jo[nameof(points)] = JsonSerializer.Serialize(points);
                jo[nameof(core)] = JsonSerializer.Serialize(core);
            }
            return jo;
        }

#if UNITY_EDITOR
        protected void OnDrawGizmos() {
            Gizmos.color = color;
            Vector3 center = get_center();
            float scale = HandleUtility.GetHandleSize(center) * 0.2f;
            Gizmos.DrawSphere(center, scale);
            foreach (GameObject point in points) {
                scale = HandleUtility.GetHandleSize(point.transform.position) * 0.1f;
                Gizmos.DrawSphere(point.transform.position, scale);
                Handles.color = Color.blue;
                Handles.ArrowHandleCap(0,
                                       point.transform.position,
                                       point.transform.rotation,
                                       scale * 3,
                                       Event.current.type);
            }
        }

    [CustomEditor(typeof(PointGroup))]
    public class Insp_PointGroup : Editor {
        public override void OnInspectorGUI() {
            DrawDefaultInspector();
            if (GUILayout.Button("复制ID")) {
                GUIUtility.systemCopyBuffer = ((PointGroup)target).id.ToString();
            }
        }
    }
#endif
}