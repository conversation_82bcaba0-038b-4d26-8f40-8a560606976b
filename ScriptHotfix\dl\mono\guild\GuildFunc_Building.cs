﻿using System;
using pure.refactor.attributes;
using pure.utils.dlltools;
using UnityEngine;
#if UNITY_EDITOR
using UnityEditor;
using pure.utils.json;
using pure.utils.mathTools;
using UnityEditorInternal;
#endif

namespace dl.mono.guild {
	[MonoDll(GuildMonoType.GUILD_BUILDING, MonoDllTags.Server)]
	public sealed partial class GuildFunc_Building : MonoBehaviour {
		[Serializable]
		public struct BuildPosition {
			[Excel("GuildBuild")]
			public int build;

			public GameObject position;
		}

		public BuildPosition[] builds = new BuildPosition[0];
	}
}

#if UNITY_EDITOR
namespace dl.mono.guild {
	public partial class GuildFunc_Building : IJsonable {
		public JsonObject ToJson(JsonDataUsage usage) {
			foreach (BuildPosition b in builds) {
				if (!b.position) {
					throw new Exception($"{gameObject.scene.name} {name}, build position lost");
				}
			}
			JsonObject jo = new JsonObject { [nameof(builds)] = JsonSerializer.Serialize(builds) };
			return jo;
		}

		public JsonDataUsage jsonUsage { get { return JsonDataUsage.ServerSide; } }

#if UNITY_EDITOR
		internal void OnDrawGizmos() {
			foreach (BuildPosition def in builds) {
				GameObject o = def.position;
				if (!o) {
					continue;
				}
				Handles.matrix = o.transform.localToWorldMatrix;
				Handles.color = Color.green;
				Handles.DrawWireDisc(Vector3.zero, Vector3.up, 2);
				Handles.matrix = Matrix4x4.identity;
				Vector3 src = o.transform.position;
				Vector3 dst = o.transform.rotation * Vector3.forward * 5;
				Handles.color = Color.red;
				Handles.DrawLine(src, src + dst);
			}
		}
#endif
	}
}

namespace dl.mono.guild {
	[CustomEditor(typeof(GuildFunc_Building))]
	public class Insp_GuildBuild : Editor {
		private ReorderableList _list;

		protected void OnEnable() {
			_list = new ReorderableList(serializedObject,
			                            serializedObject.FindProperty(nameof(GuildFunc_Building.builds))) {
				drawElementCallback = draw_element,
				drawHeaderCallback = draw_head
			};
		}

		public override void OnInspectorGUI() {
			serializedObject.Update();
			_list.DoLayoutList();
			serializedObject.ApplyModifiedProperties();
		}

		private static void draw_head(Rect rect) {
			rect = rect.ContractTo(rect.width, 16);
			Rect item = rect;
			item.width = item.width / 2 - 5;
			GUI.Label(item, "建筑");
			item.x += item.width + 5;
			GUI.Label(item, "位置");
		}

		private void draw_element(Rect rect, int index, bool f, bool a) {
			rect = rect.ContractTo(rect.width, 16);
			SerializedProperty p = _list.serializedProperty.GetArrayElementAtIndex(index);
			Rect item = rect;
			item.width = item.width / 2 - 5;
			{
				SerializedProperty b = p.FindPropertyRelative(nameof(GuildFunc_Building.BuildPosition.build));
				EditorGUI.PropertyField(item, b, GUIContent.none);
			}
			item.x += item.width + 5;
			{
				SerializedProperty b = p.FindPropertyRelative(nameof(GuildFunc_Building.BuildPosition.position));
				EditorGUI.PropertyField(item, b, GUIContent.none);
			}
		}
	}
}

#endif