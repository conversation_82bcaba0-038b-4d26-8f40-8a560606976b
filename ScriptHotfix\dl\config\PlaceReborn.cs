﻿using UnityEngine;
#if UNITY_EDITOR
using System;
using System.ComponentModel;
using pure.refactor.attributes;
using pure.utils.dlltools;
using pure.utils.json;
using pure.utils.mathTools;
using UnityEditor;
using UnityEditorInternal;
using XLua;
#endif

namespace dl.config {
	public partial class PlaceReborn : MonoBehaviour { }

#if UNITY_EDITOR
	[MonoDll("place_reborn", MonoDllTags.Server)]
	public partial class PlaceReborn : IJsonable {
		public enum RebornType {
			[Description("道具复活")]
			Drugs = 0,

			[Description("复活点复活")]
			Point,

			[Description("返回主城")]
			Backhome,

			[Description("免费复活")]
			Free,
		}

		public enum Side {
			[Description("红方")]
			Left = 0,

			[Description("蓝方")]
			Right = 1,

			[Description("第三方")]
			ThirdParty = 2,
		}

		[Serializable]
		public struct Location {
			public GameObject point;
			public Side side;
		}

		[Serializable]
		public struct Policy {
			public RebornType type;
			public int count;
			public float beginTime;
			public float endTime;

			[Excel("DailyOpt")]
			public int dailyOptId;

			public bool resetDailyOpt;
		}

		public Policy[] policies = new Policy[0];

		public Location[] locations = new Location[0];
		public JsonDataUsage jsonUsage { get { return JsonDataUsage.ClientSide | JsonDataUsage.ServerSide; } }

		[BlackList]
		public JsonObject ToJson(JsonDataUsage usage) {
			JsonObject jo = new JsonObject {
				["monoType"] = "place_reborn",
				[nameof(policies)] = JsonSerializer.Serialize(policies),
				[nameof(locations)] = JsonSerializer.Serialize(locations)
			};
			if (Check(out string err)) {
				Debug.LogError(err);
			}
			return jo;
		}

		public bool Check(out string str) {
			str = string.Empty;
			foreach (Location l in locations) {
				if (!l.point) {
					str += string.Format("location[{0}] point lost\r\n", gameObject.scene.name);
				}
			}
			bool usep = false;
			foreach (Policy p in policies) {
				if (p.type == RebornType.Point) {
					usep = true;
					break;
				}
			}
			if (usep && locations.Length == 0) {
				str += string.Format("location[{0}] location is empty", gameObject.scene.name);
			}
			return str.Length > 0;
		}

#if UNITY_EDITOR
		protected void OnDrawGizmos() {
			Gizmos.color = Color.red;
			foreach (Location point in locations) {
				if (!point.point) {
					return;
				}
				float scale = HandleUtility.GetHandleSize(point.point.transform.position) * 0.1f;
				Gizmos.DrawSphere(point.point.transform.position, scale);
				Handles.color = Color.red;
				Handles.ArrowHandleCap(0,
				                       point.point.transform.position,
				                       point.point.transform.rotation,
				                       scale * 3,
				                       Event.current.type);
			}
		}

	[CustomEditor(typeof(PlaceReborn))]
	internal class Insp_PlaceReborn : Editor {
		private ReorderableList _list;
		private SerializedProperty _polices;
		private SerializedProperty _locations;

		protected void OnEnable() {
			_locations = serializedObject.FindProperty(nameof(PlaceReborn.locations));
			_polices = serializedObject.FindProperty(nameof(PlaceReborn.policies));
			_list = new ReorderableList(serializedObject, _polices) { drawElementCallback = draw_policy };
		}

		public override void OnInspectorGUI() {
			serializedObject.Update();
			{
				_list.serializedProperty = _polices;
				_list.drawElementCallback = draw_policy;
				_list.drawHeaderCallback = draw_policy_head;
				_list.DoLayoutList();
			}
			{
				_list.serializedProperty = _locations;
				_list.drawElementCallback = draw_location;
				_list.drawHeaderCallback = draw_location_head;
				_list.DoLayoutList();
			}
			serializedObject.ApplyModifiedProperties();
			PlaceReborn t = (PlaceReborn)target;
			if (t.Check(out string str)) {
				EditorGUILayout.HelpBox(str, MessageType.Error);
			}
		}

		#region ---------------------policy-----------------------------

		private static void draw_policy_head(Rect rect) {
			Rect item = rect.ContractTo(rect.width, 16);
			item.x += 16;
			{
				item.width = 80;
				GUI.Label(item, "类型");
				item.x += item.width + 5;
			}
			{
				item.width = 50;
				GUI.Label(item, "起始");
				item.x += item.width + 5;
			}
			{
				item.width = 50;
				GUI.Label(item, "结束");
				item.x += item.width + 5;
			}
			{
				item.width = 50;
				GUI.Label(item, "计数");
				item.x += item.width + 5;
			}
			{
				item.width = 30;
				GUI.Label(item, new GUIContent("重置", "重置每日次数"));
				item.x += item.width + 5;
			}
			{
				item.width = rect.xMax - item.x;
				GUI.Label(item, "每日次数");
			}
		}

		private void draw_policy(Rect rect, int index, bool focus, bool active) {
			Rect item = rect.ContractTo(rect.width, 16);
			SerializedProperty p = _polices.GetArrayElementAtIndex(index);
			{
				item.width = 80;
				EditorGUI.PropertyField(item,
				                        p.FindPropertyRelative(nameof(PlaceReborn.Policy.type)),
				                        GUIContent.none);
				item.x += item.width + 5;
			}
			{
				item.width = 50;
				EditorGUI.PropertyField(item,
				                        p.FindPropertyRelative(nameof(PlaceReborn.Policy.beginTime)),
				                        GUIContent.none);
				item.x += item.width + 5;
			}
			{
				item.width = 50;
				EditorGUI.PropertyField(item,
				                        p.FindPropertyRelative(nameof(PlaceReborn.Policy.endTime)),
				                        GUIContent.none);
				item.x += item.width + 5;
			}
			{
				item.width = 50;
				EditorGUI.PropertyField(item,
				                        p.FindPropertyRelative(nameof(PlaceReborn.Policy.count)),
				                        GUIContent.none);
				item.x += item.width + 5;
			}
			{
				item.width = 30;
				EditorGUI.PropertyField(item,
				                        p.FindPropertyRelative(nameof(PlaceReborn.Policy.resetDailyOpt)),
				                        GUIContent.none);
				item.x += item.width + 5;
			}
			{
				item.width = rect.xMax - item.x;
				EditorGUI.PropertyField(item,
				                        p.FindPropertyRelative(nameof(PlaceReborn.Policy.dailyOptId)),
				                        GUIContent.none);
			}
		}

		#endregion

		#region -----------------------location-------------------

		private static void draw_location_head(Rect rect) {
			Rect item = rect.ContractTo(rect.width, 16);
			item.x += 16;
			{
				item.width = 80;
				GUI.Label(item, "类型");
				item.x += item.width + 5;
			}
			{
				item.width = rect.xMax - item.x;
				GUI.Label(item, "位置");
			}
		}

		private void draw_location(Rect rect, int index, bool focus, bool active) {
			Rect item = rect.ContractTo(rect.width, 16);
			SerializedProperty p = _locations.GetArrayElementAtIndex(index);
			{
				item.width = 80;
				EditorGUI.PropertyField(item,
				                        p.FindPropertyRelative(nameof(PlaceReborn.Location.side)),
				                        GUIContent.none);
				item.x += item.width + 5;
			}
			{
				item.width = rect.xMax - item.x;
				EditorGUI.PropertyField(item,
				                        p.FindPropertyRelative(nameof(PlaceReborn.Location.point)),
				                        GUIContent.none);
			}
		}

		#endregion
	}
#endif
}