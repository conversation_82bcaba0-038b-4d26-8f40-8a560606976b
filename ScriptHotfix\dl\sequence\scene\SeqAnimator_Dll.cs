﻿using game.ai.entity;
using pure.database.structure;
using UnityEngine;
using ASlot = game.ai.entity.AnimCollection.Slot;

namespace dl.sequence.scene {
    public abstract class SeqAnimator_Dll : MonoBehaviour, IAnimatorController {
        public SeqAnimatorChangeCallback OnChange;

        public SeqAsset_Dll asset;

        private SeqAnimatorController _controller = new SeqAnimatorController();

        public SeqAnimatorController.RenderInfo renderInfo { get { return _controller.renderInfo; } }
        public int frame { get { return _controller.frame; } }
        public int direction { get { return _controller.direction; } }
        public SeqState state { get { return _controller.state; } }

        protected void Awake() {
            _controller.asset = asset;
        }

        protected void OnEnable() {
            _controller.active = true;
        }

        public void SetValue(int hash, AnimatorControllerParameterType type, float v) {
            _controller.SetValue(hash, type, v);
        }

        public void CopyFrom(SeqAnimator_Dll src) {
            _controller.CopyFrom(src._controller);
        }

        public void SetMainFrame(int val) {
            _controller.SetMainFrame(val);
        }

        public bool active { get { return isActiveAndEnabled; } }

        public float speed { get { return _controller.speed; } set { _controller.speed = value; } }

        protected void Update() { 
         
            bool dirty = _controller.Update(transform.eulerAngles.y);
            
            if (dirty && OnChange != null) {
                SeqAnimEvent evt = new SeqAnimEvent {
                    asset = asset,
                    frame = _controller.frame,
                    dir = _controller.direction,
                    state = _controller.state
                };
                OnChange(ref evt);
            }
        }

        public bool GetSlot(int hash, out ASlot slot) {
            return _controller.GetSlot(hash, out slot);
        }

        protected void OnDisable() {
            _controller.active = false;
        }

        protected void OnDestroy() {
            _controller.active = false;
            _controller.asset = null;
        }

        public void GetParameter(out string[] names, out AnimatorControllerParameter[] parameters) {
            if (!asset) {
                names = ZeroBuffer<string>.Buffer;
                parameters = ZeroBuffer<AnimatorControllerParameter>.Buffer;
                return;
            }
            names = new string[asset.parameters.Length];
            parameters = new AnimatorControllerParameter[asset.parameters.Length];
            for (int i = 0; i < asset.parameters.Length; ++i) {
                names[i] = asset.parameters[i].name;
                parameters[i] = new AnimatorControllerParameter {
                    name = asset.parameters[i].name,
                    type = asset.parameters[i].type,
                };
            }
        }

#if UNITY_EDITOR
        protected void OnValidate() {
            _controller.asset = asset;
        }
#endif
    }
}