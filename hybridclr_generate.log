[Licensing::Mo<PERSON><PERSON>] Trying to connect to existing licensing client channel...
[Licensing::IpcConnector] Successfully connected to the License Client on channel: "LicenseClient-SpiderChan"
[Licensing::Client] Error: Code 10 while verifying Licensing Client signature (process Id: 11796, path: "C:/Program Files/Unity Hub/UnityLicensingClient_V1/Unity.Licensing.Client.exe")
[Licensing::Module] Error: LicensingClient has failed validation; ignoring
[Licensing::Client] Handshaking with LicensingClient:
  Version:                 1.17.0+aa6cfba
  Session Id:              912d1c12821f4bd3a41cb2d343c174e8
  Correlation Id:          720b813376d45b2984075541a8018429
  External correlation Id: 8165734520210373366
  Machine Id:              oouJfHp3z9PgYZFLQ0scC28Xxho=
[Licensing::Module] Successfully connected to LicensingClient on channel: "LicenseClient-Spider<PERSON>han" (connect: 0.00s, validation: 0.01s, handshake: 0.07s)
[Licensing::IpcConnector] Successfully connected to the License Notification on channel: "LicenseClient-SpiderChan-notifications"
Entitlement-based licensing initiated
[Licensing::Module] Error: Access token is unavailable; failed to update
[Licensing::Client] Successfully updated license
Built from '2021.3/staging' branch; Version is '2021.3.26f1 (a16dc32e0ff2) revision 10579395'; Using compiler version '192829333'; Build Type 'Release'
OS: 'Windows 10  (10.0.19045) 64bit Professional' Language: 'zh' Physical Memory: 32699 MB
[Licensing::Client] Successfully resolved entitlements
[Licensing::Module] Serial number assigned to: "ewogICJzdWJzR3JvdXAiIDogInVuaXR5IGVkaXRvciIsCiAgIm5hbWVzcGFjZSIgOiAidW5pdHlfZWRpdG9yIiwKICAidHlwZSIgOiAiRURJVE9SIiwKICAidGFnIiA6ICJVbml0eVBlcnNvbmFsXXXX"
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 0
[Package Manager] Server::Start -- Port 51791 was selected

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\2021.3.26f1\Editor\Unity.exe
-batchmode
-quit
-projectPath
d:\BaiduNetdiskDownload\rc\Assets
-executeMethod
HybridCLR.Editor.Commands.PrebuildCommand.GenerateAll
-logFile
hybridclr_generate.log
Couldn't set project path to: D:/BaiduNetdiskDownload/rc/Assets/d:/BaiduNetdiskDownload/rc/Assets
Exiting without the bug reporter. Application will terminate with return code 1